# Markdown标题修复工具

这是一个用于修复Markdown文档中标题层级和格式问题的Python工具。

## 功能特性

- **智能标题识别**：自动识别各种格式的标题候选行
- **层级修复**：修正标题层级，确保连续性（h1->h2->h3，不跳级）
- **多种输入格式支持**：
  - 标准Markdown标题（`# ## ###`）
  - 数字编号标题（`1. 2. 3.`）
  - 中文标点数字标题（`1、2、3、`）
  - 中文章节标题（`第一章 第二节`）
  - 无格式标题文本
- **多种分析方法**：
  - 基于规则的本地处理（默认）
  - OpenAI API集成
- **灵活输出**：支持原地修改或输出到新文件

## 安装要求

```bash
pip install requests chardet
```

## 使用方法

### 基本用法

```bash
# 使用规则引擎修复标题（推荐）
python clade_code/markdown_heading_fixer.py document.md

# 预览修复效果（不修改文件）
python clade_code/markdown_heading_fixer.py document.md --dry-run --verbose

# 输出到新文件
python clade_code/markdown_heading_fixer.py document.md -o fixed_document.md
```

### 使用LLM API

```bash
# 使用OpenAI API
python clade_code/markdown_heading_fixer.py document.md \
  --llm-provider openai \
  --api-key your-api-key \
  --model gpt-4
```

### 环境变量

可以通过环境变量设置API密钥：

```bash
export OPENAI_API_KEY="your-openai-key"
```

## 命令行参数

- `file_path`: 要处理的Markdown文件路径
- `-o, --output`: 输出文件路径（默认原地修改）
- `-d, --dry-run`: 预览模式，不修改文件
- `-v, --verbose`: 显示详细信息
- `--max-heading-length`: API请求中标题内容的最大长度（默认100）
- `--llm-provider`: 分析提供商（rule_based/openai）
- `--api-key`: OpenAI API密钥
- `--base-url`: 自定义OpenAI API端点URL
- `--model`: 使用的OpenAI模型名称

## 修复规则

### 标题识别
工具会识别以下格式的潜在标题：
- `# ## ###` - 标准Markdown标题
- `1. 2. 3.` - 数字编号标题
- `1、2、3、` - 中文标点数字标题
- `第一章 第二节` - 中文章节标题
- 短行文本（可能是标题）

### 层级修复
- 确保第一个标题是h1
- 标题层级连续，不跳级
- 数字编号标题根据编号模式推断层级
- 已有h1的情况下，新的主要章节设为h2

### 特殊处理
- 总结、结论等关键词自动设为h2
- 保留原有的数字编号
- 清理多余的空格和格式

## 示例

### 输入文档
```markdown
# 文档标题

## 1. 第一章

### 1.1 子章节

2. 第二章

这应该是标题但格式不对。

#### 2.1.1 深层标题

第三章

5、第五章

## 总结
```

### 修复后
```markdown
# 文档标题

## 1. 第一章

### 1.1 子章节

## 2. 第二章

## 这应该是标题但格式不对。

### 2.1.1 深层标题

## 第三章

## 5、第五章

## 总结
```

## 注意事项

1. **备份重要文件**：建议在处理重要文档前先备份
2. **预览功能**：使用`--dry-run`参数预览修复效果
3. **编码支持**：自动检测文件编码，支持UTF-8等常见编码
4. **API限制**：使用LLM API时注意请求频率和成本
5. **规则引擎**：默认的规则引擎适用于大多数情况，无需API密钥

## 故障排除

- **文件编码问题**：工具会自动检测编码，如有问题请确保文件是有效的文本格式
- **API错误**：检查API密钥和网络连接，工具会自动回退到规则引擎
- **层级问题**：复杂的文档结构可能需要手动调整，建议使用verbose模式查看详细信息
