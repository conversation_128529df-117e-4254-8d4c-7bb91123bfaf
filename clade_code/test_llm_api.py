#!/usr/bin/env python3
"""
测试大模型API连接的脚本
"""

import requests
import json

def test_api_connection():
    """测试API连接"""
    
    # 您的API配置
    base_url = "http://ai.ai.iot.chinamobile.com/imaas/v1"
    api_key = "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Dbapi_key"
    model = "Qwen3-235B-A22B"
    
    # 确保URL格式正确
    if not base_url.endswith("/"):
        base_url += "/"
    endpoint = f"{base_url}chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 简单的测试消息
    test_payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": "请回复'连接成功'"}
        ],
        "temperature": 0.2,
        "max_tokens": 100
    }
    
    try:
        print(f"正在测试API连接: {endpoint}")
        print(f"使用模型: {model}")
        
        response = requests.post(endpoint, headers=headers, json=test_payload, timeout=30)
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            content = response_data["choices"][0]["message"]["content"]
            print(f"API响应成功: {content}")
            return True
        else:
            print(f"API请求失败: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("API请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("无法连接到API服务器")
        return False
    except Exception as e:
        print(f"API测试出错: {e}")
        return False

def test_markdown_analysis():
    """测试Markdown分析功能"""
    
    base_url = "http://ai.ai.iot.chinamobile.com/imaas/v1"
    api_key = "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Dbapi_key"
    model = "Qwen3-235B-A22B"
    
    if not base_url.endswith("/"):
        base_url += "/"
    endpoint = f"{base_url}chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 测试用的标题分析任务
    task_description = """Markdown标题修正任务，请按以下规则处理：

1. 格式识别：
   - 识别真正的标题行（应该转换为markdown标题）
   - 识别伪标题（看起来像标题但实际是正文）

2. 层级规则：
   - 标题层级应该连续，不跳级（h1->h2->h3，不能h1->h3）
   - 文档第一个标题必须是h1

3. 输出格式：
   请直接返回JSON格式：
   {
     "corrections": [
       {
         "line_number": 行号,
         "original_line": "原始内容",
         "corrected_line": "修正后的markdown标题",
         "reason": "修正原因"
       }
     ]
   }"""
    
    input_lines = """1: 系统操作手册
3: 1. 用户管理
5: 1.1 用户注册"""
    
    full_prompt = f"{task_description}\n\n待处理的疑似标题行：\n{input_lines}"
    
    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": full_prompt}
        ],
        "temperature": 0.2,
        "max_tokens": 1000
    }
    
    try:
        print("\n正在测试Markdown分析功能...")
        
        response = requests.post(endpoint, headers=headers, json=payload, timeout=60)
        
        if response.status_code == 200:
            response_data = response.json()
            content = response_data["choices"][0]["message"]["content"]
            print(f"分析结果: {content}")
            
            # 尝试解析JSON
            try:
                if "```json" in content:
                    start = content.find("```json") + 7
                    end = content.find("```", start)
                    if end != -1:
                        content = content[start:end].strip()
                
                result = json.loads(content)
                print("JSON解析成功!")
                print(f"找到 {len(result.get('corrections', []))} 个修正项")
                return True
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                return False
        else:
            print(f"分析请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"分析测试出错: {e}")
        return False

if __name__ == "__main__":
    print("=== 大模型API测试 ===")
    
    # 测试基本连接
    if test_api_connection():
        print("\n✅ API连接测试通过")
        
        # 测试Markdown分析
        if test_markdown_analysis():
            print("\n✅ Markdown分析测试通过")
            print("\n🎉 所有测试通过！您可以使用大模型功能了。")
        else:
            print("\n❌ Markdown分析测试失败")
    else:
        print("\n❌ API连接测试失败")
        print("请检查网络连接和API配置")
