#!/usr/bin/env python3
"""
Markdown标题修正工具 - 简化运行脚本
使用您的大模型API配置
"""

import os
import sys
import argparse
from markdown_heading_fixer import (
    read_markdown_file,
    clean_existing_headings,
    extract_heading_candidates,
    prepare_llm_input,
    analyze_with_llm,
    update_markdown_content
)

# 导入配置
try:
    from config import get_config
    CONFIG = get_config()
    DEFAULT_CONFIG = CONFIG["llm"]
    FALLBACK_TO_RULES = CONFIG["fallback"]
except ImportError:
    print("警告: 无法导入config.py，使用默认配置")
    DEFAULT_CONFIG = {
        "provider": "openai",
        "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
        "api_key": "请在config.py中设置您的API密钥",
        "model": "gpt-4"
    }
    FALLBACK_TO_RULES = True

def process_markdown_file(file_path: str, output_path: str = None, dry_run: bool = False, 
                         use_llm: bool = True, verbose: bool = True):
    """
    处理Markdown文件的标题层级
    
    Args:
        file_path: 输入文件路径
        output_path: 输出文件路径（如果为None，则覆盖原文件）
        dry_run: 是否只预览不实际修改
        use_llm: 是否使用大模型分析
        verbose: 是否显示详细信息
    """
    
    # 检查文件是否存在
    if not os.path.isfile(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return False
    
    print(f"正在处理文件: {file_path}")
    
    try:
        # 1. 读取文件
        lines, line_ending, encoding = read_markdown_file(file_path)
        print(f"文件编码: {encoding}, 行结束符: {repr(line_ending)}")
        
        # 2. 清理现有标题格式
        cleaned_lines = clean_existing_headings(lines)
        
        # 3. 提取标题候选
        candidates = extract_heading_candidates(cleaned_lines)
        
        if not candidates:
            print("未找到标题候选项")
            return True
        
        print(f"找到 {len(candidates)} 个标题候选项")
        
        if verbose:
            print("\n标题候选项:")
            for i, candidate in enumerate(candidates[:10]):  # 只显示前10个
                print(f"  {i+1}. 第{candidate['line_num']}行: {candidate['content'][:60]}...")
            if len(candidates) > 10:
                print(f"  ... 还有 {len(candidates) - 10} 个候选项")
        
        # 4. 准备分析输入
        llm_input = prepare_llm_input(candidates)
        
        # 5. 进行分析
        if use_llm:
            print("使用大模型进行分析...")
            config = DEFAULT_CONFIG

            # 检查API密钥是否有效
            if config.get("api_key") == "请在config.py中设置您的API密钥" or not config.get("api_key"):
                print("警告: API密钥未设置或无效")
                if FALLBACK_TO_RULES:
                    print("自动回退到规则引擎...")
                    config = {"provider": "rule_based"}
                else:
                    print("请在config.py中设置正确的API密钥")
                    return False
        else:
            print("使用规则引擎进行分析...")
            config = {"provider": "rule_based"}

        try:
            analysis_response = analyze_with_llm(llm_input, config=config)
        except Exception as e:
            print(f"分析过程出错: {e}")
            if use_llm and FALLBACK_TO_RULES:
                print("回退到规则引擎...")
                config = {"provider": "rule_based"}
                analysis_response = analyze_with_llm(llm_input, config=config)
            else:
                raise
        
        # 6. 处理结果
        corrections = analysis_response.get("corrections", [])
        
        if not corrections:
            print("无需修正")
            return True
        
        print(f"需要应用 {len(corrections)} 个修正")
        
        if verbose or dry_run:
            print("\n修正详情:")
            for correction in corrections:
                line_num = correction["line_number"]
                original = correction.get("original_line", "")
                corrected = correction.get("corrected_line", "DELETE")
                print(f"  第{line_num}行: {original[:40]}... -> {corrected[:40]}...")
        
        if not dry_run:
            # 7. 应用修正
            updated_content = update_markdown_content(
                cleaned_lines,
                corrections,
                line_ending
            )
            
            # 8. 保存文件
            output_file = output_path if output_path else file_path
            
            with open(output_file, 'w', encoding=encoding, newline='') as f:
                f.write(updated_content)
            
            print(f"成功保存到: {output_file}")
            print(f"应用了 {len(corrections)} 个修正")
        else:
            print("预览模式 - 未实际修改文件")
        
        return True
        
    except Exception as e:
        print(f"处理过程中出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Markdown标题修正工具 - 简化版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_markdown_fixer.py document.md                    # 使用大模型修正
  python run_markdown_fixer.py document.md --no-llm          # 使用规则引擎
  python run_markdown_fixer.py document.md --dry-run         # 预览修正
  python run_markdown_fixer.py document.md -o output.md      # 保存到新文件
        """
    )
    
    parser.add_argument("file_path", help="要处理的Markdown文件路径")
    parser.add_argument("-o", "--output", help="输出文件路径（默认覆盖原文件）")
    parser.add_argument("--dry-run", action="store_true", help="只预览不实际修改")
    parser.add_argument("--no-llm", action="store_true", help="使用规则引擎而不是大模型")
    parser.add_argument("--quiet", action="store_true", help="减少输出信息")
    
    args = parser.parse_args()
    
    success = process_markdown_file(
        file_path=args.file_path,
        output_path=args.output,
        dry_run=args.dry_run,
        use_llm=not args.no_llm,
        verbose=not args.quiet
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
