# Markdown标题修正工具

这是一个智能的Markdown文档标题层级修正工具，专门用于处理从Word文档转换而来的Markdown文件中标题层级混乱的问题。

## 功能特点

- **智能识别**: 自动识别真正的标题和伪标题（被误认为标题的正文）
- **层级修正**: 根据内容结构自动分配正确的标题层级（H1-H6）
- **双重引擎**: 支持大模型分析和规则引擎两种处理方式
- **格式保持**: 保持原文档的编码和行结束符格式
- **预览模式**: 支持预览修改而不实际更改文件

## 安装依赖

```bash
pip install requests chardet
```

## 使用方法

### 方法1: 使用简化脚本（推荐）

```bash
# 使用大模型进行智能分析（推荐）
python run_markdown_fixer.py your_document.md

# 预览修改（不实际更改文件）
python run_markdown_fixer.py your_document.md --dry-run

# 使用规则引擎（不需要API）
python run_markdown_fixer.py your_document.md --no-llm

# 保存到新文件
python run_markdown_fixer.py your_document.md -o fixed_document.md
```

### 方法2: 使用完整版本

```bash
# 使用您的大模型API
python markdown_heading_fixer.py your_document.md \
  --llm-provider openai \
  --base-url "http://ai.ai.iot.chinamobile.com/imaas/v1" \
  --api-key "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Dbapi_key" \
  --model "gpt-4" \
  --verbose

# 使用规则引擎
python markdown_heading_fixer.py your_document.md \
  --llm-provider rule_based \
  --verbose
```

## 工具原理

1. **清理阶段**: 移除现有的Markdown标题标记（#符号）
2. **提取阶段**: 识别潜在的标题候选项，包括：
   - 数字编号的章节（1. 2. 3.）
   - 多级编号（1.1 1.2 1.1.1）
   - 中文编号（1、2、3、）
   - 中文章节（第一章、第二节）
   - 功能模块名称（管理、查询、统计等）
3. **分析阶段**: 使用大模型或规则引擎判断真正的标题
4. **修正阶段**: 应用正确的Markdown标题格式

## 识别规则

工具会自动过滤以下内容，避免将操作步骤误认为标题：
- 包含操作动词的句子（点击、输入、选择等）
- 包含UI元素的描述（按钮、页面、弹窗等）
- 过长的描述性文本
- 以标点符号结尾的句子

## 测试示例

使用提供的测试文件：

```bash
# 预览修正效果
python run_markdown_fixer.py test_document.md --dry-run

# 应用修正
python run_markdown_fixer.py test_document.md -o test_document_fixed.md
```

## 配置说明

大模型API配置已预设在 `run_markdown_fixer.py` 中：
- Base URL: `http://ai.ai.iot.chinamobile.com/imaas/v1`
- API Key: `sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Dbapi_key`
- Model: `gpt-4`

如需修改配置，请编辑 `run_markdown_fixer.py` 中的 `DEFAULT_CONFIG` 变量。

## 注意事项

1. 建议先使用 `--dry-run` 预览修改效果
2. 处理重要文档前请备份原文件
3. 大模型分析效果更好，但需要网络连接
4. 规则引擎可离线使用，适合批量处理

## 文件说明

- `markdown_heading_fixer.py`: 完整版工具，支持所有参数
- `run_markdown_fixer.py`: 简化版脚本，预配置您的API
- `config_example.py`: 配置示例和使用说明
- `test_document.md`: 测试用的示例文档
- `README_zh.md`: 中文说明文件
