# Markdown标题修正工具 - 使用指南

## 工具概述

这个工具专门用于修正从Word文档转换而来的Markdown文件中的标题层级问题。它能够：

1. **智能识别真正的标题**：区分真正的标题和被误认为标题的正文内容
2. **自动分配层级**：根据内容结构自动分配H1-H6标题层级
3. **支持多种格式**：支持数字编号、中文编号、章节标题等多种格式
4. **双重引擎**：支持大模型智能分析和规则引擎两种处理方式

## 快速开始

### 1. 基本使用（推荐）

```bash
# 使用大模型进行智能分析
python run_markdown_fixer.py your_document.md

# 预览修改效果（不实际修改文件）
python run_markdown_fixer.py your_document.md --dry-run

# 保存到新文件
python run_markdown_fixer.py your_document.md -o fixed_document.md
```

### 2. 使用规则引擎（离线模式）

```bash
# 不使用大模型，仅使用规则引擎
python run_markdown_fixer.py your_document.md --no-llm
```

### 3. 使用完整版本（高级用户）

```bash
# 使用完整参数
python markdown_heading_fixer.py your_document.md \
  --llm-provider openai \
  --base-url "http://ai.ai.iot.chinamobile.com/imaas/v1" \
  --api-key "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db" \
  --model "Qwen3-235B-A22B" \
  --verbose
```

## 配置说明

### API配置

工具已预配置您的大模型API：
- **Base URL**: `http://ai.ai.iot.chinamobile.com/imaas/v1`
- **API Key**: `sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db`
- **Model**: `Qwen3-235B-A22B`

如需修改配置，请编辑 `config.py` 文件。

### 自动回退机制

- 如果大模型API不可用，工具会自动回退到规则引擎
- 规则引擎可以离线工作，适合批量处理

## 支持的标题格式

工具能识别以下类型的标题：

1. **数字编号**: `1. 用户管理`, `2. 系统设置`
2. **多级编号**: `1.1 用户注册`, `1.2 用户登录`, `1.1.1 详细步骤`
3. **中文编号**: `1、下载安装包`, `2、解压文件`
4. **章节标题**: `第一章 系统概述`, `第二章 安装指南`
5. **功能模块**: `报表统计`, `数据分析`, `权限管理`

## 过滤规则

工具会自动过滤以下内容，避免误判：

- 包含操作动词的句子（点击、输入、选择等）
- UI元素描述（按钮、页面、弹窗等）
- 过长的描述性文本（超过100字符）
- 以标点符号结尾的句子

## 使用示例

### 示例1：处理测试文档

```bash
# 预览修正效果
python run_markdown_fixer.py test_document.md --dry-run

# 应用修正
python run_markdown_fixer.py test_document.md -o test_document_fixed.md
```

### 示例2：批量处理

```bash
# 使用规则引擎批量处理多个文件
for file in *.md; do
    python run_markdown_fixer.py "$file" --no-llm -o "fixed_$file"
done
```

## 输出示例

**修正前：**
```markdown
系统操作手册

1. 用户管理

1.1 用户注册

点击注册按钮，填写用户信息
```

**修正后：**
```markdown
系统操作手册

# 1. 用户管理

### 1.1 用户注册

点击注册按钮，填写用户信息
```

## 注意事项

1. **备份重要文档**：处理重要文档前请先备份
2. **使用预览模式**：建议先使用 `--dry-run` 预览修改效果
3. **网络连接**：大模型分析需要网络连接，规则引擎可离线使用
4. **文件编码**：工具会自动检测和保持原文件的编码格式

## 故障排除

### API连接问题
- 检查网络连接
- 验证API密钥是否正确
- 工具会自动回退到规则引擎

### 修正效果不理想
- 尝试使用大模型分析（如果可用）
- 检查原文档的标题格式是否符合识别规则
- 可以手动调整规则引擎的识别逻辑

## 文件说明

- `run_markdown_fixer.py`: 简化版脚本（推荐使用）
- `markdown_heading_fixer.py`: 完整版工具
- `config.py`: 配置文件
- `test_document.md`: 测试用例
- `test_llm_api.py`: API连接测试脚本

## 技术支持

如遇到问题，请检查：
1. Python版本（需要3.6+）
2. 依赖包是否安装（requests, chardet）
3. 文件路径是否正确
4. API配置是否有效
