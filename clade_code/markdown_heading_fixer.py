#!/usr/bin/env python3
"""
Markdown Heading Fixer

A tool to intelligently correct Markdown heading formats and hierarchy.
Supports both LLM-based analysis and rule-based correction.

Version: 2.0
Author: AI Assistant
License: MIT
"""

__version__ = "2.0"

import re
import json
import sys
import os
import argparse
import requests
from typing import List, Dict, Any, <PERSON><PERSON>


def read_markdown_file(file_path: str) -> <PERSON><PERSON>[List[str], str, str]:
    """
    Read a markdown file and return its content as lines.

    Args:
        file_path: Path to the markdown file

    Returns:
        Tuple containing:
        - List of lines in the file
        - Line ending style ('\n', '\r\n', etc.)
        - File encoding
    """
    encoding = 'utf-8'  # Default encoding
    try:
        with open(file_path, 'rb') as f:
            raw_content = f.read()

        # Try to detect encoding
        try:
            import chardet
            detected = chardet.detect(raw_content)
            if detected['confidence'] > 0.7:
                encoding = detected['encoding']
        except ImportError:
            pass  # Fallback to utf-8 if chardet is not available

        # Detect line ending
        if b'\r\n' in raw_content:
            line_ending = '\r\n'
        elif b'\n' in raw_content:
            line_ending = '\n'
        else:
            line_ending = os.linesep  # Fallback to system default

        # Decode content with detected encoding
        content = raw_content.decode(encoding)
        lines = content.splitlines()

        return lines, line_ending, encoding
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        sys.exit(1)


def clean_existing_headings(lines: List[str]) -> List[str]:
    """
    Remove existing markdown heading formats to start fresh.

    Args:
        lines: List of lines from markdown file

    Returns:
        List of lines with heading formats removed
    """
    cleaned_lines = []

    for line in lines:
        # Remove markdown heading markers
        cleaned_line = re.sub(r'^#{1,6}\s*', '', line)
        cleaned_lines.append(cleaned_line)

    return cleaned_lines


def extract_heading_candidates(lines: List[str]) -> List[Dict[str, Any]]:
    """
    Extract candidate heading lines from markdown content.
    Focus on structural elements that should be headings.

    Args:
        lines: List of lines from markdown file

    Returns:
        List of dictionaries with candidate heading line information
    """
    candidates = []

    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if not line_stripped:
            continue

        is_candidate = False
        heading_type = None

        # 1. Document title (first few lines, contains key words)
        if i < 5 and re.search(r'(手册|文档|指南|说明书|操作指南|用户手册)', line_stripped):
            if len(line_stripped) < 50 and not re.search(r'(点击|操作|步骤)', line_stripped):
                is_candidate = True
                heading_type = "document_title"

        # 2. Numbered section headings (clear hierarchical structure)
        elif re.match(r'^[0-9]+\.?\s+[^\d]', line_stripped):  # 1. 标题 (not 1. 点击)
            # Must be short and not contain operation words
            if (len(line_stripped) < 80 and
                not re.search(r'(点击|扫描|输入|选择|填写|完成|提交|确认|返回|弹出)', line_stripped) and
                not re.search(r'(按钮|页面|操作|步骤|功能|任务|订单|商品|条码|弹窗|列表)', line_stripped)):
                is_candidate = True
                heading_type = "numbered_section"

        # 3. Sub-numbered headings (1.1, 1.2, etc.)
        elif re.match(r'^[0-9]+\.[0-9]+\.?\s+', line_stripped):
            if (len(line_stripped) < 60 and
                not re.search(r'(点击|扫描|输入|选择|填写|完成|提交|确认)', line_stripped) and
                not re.search(r'(按钮|页面|操作|步骤)', line_stripped)):
                is_candidate = True
                heading_type = "sub_numbered"

        # 4. Deep numbered headings (1.1.1, etc.)
        elif re.match(r'^[0-9]+\.[0-9]+\.[0-9]+\.?\s+', line_stripped):
            if (len(line_stripped) < 50 and
                not re.search(r'(点击|扫描|输入|操作|步骤)', line_stripped)):
                is_candidate = True
                heading_type = "deep_numbered"

        # 5. Chinese numbered headings (1、2、etc.)
        elif re.match(r'^[0-9]+[、，]\s*', line_stripped):
            if (len(line_stripped) < 60 and
                not re.search(r'(点击|扫描|输入|选择|填写)', line_stripped) and
                not re.search(r'(按钮|页面|操作|步骤)', line_stripped)):
                is_candidate = True
                heading_type = "chinese_numbered"

        # 6. Chinese chapter headings
        elif re.match(r'^第[零一二三四五六七八九十]+[章节部分]', line_stripped):
            is_candidate = True
            heading_type = "chinese_chapter"

        # 7. Management/functional module names - REMOVED
        # These should be treated as regular content, not headings

        if is_candidate:
            candidates.append({
                "line_num": i + 1,  # 1-indexed line numbers
                "content": line,
                "position": i,      # 0-indexed position for internal use
                "type": heading_type
            })

    return candidates


def prepare_llm_input(candidates: List[Dict[str, Any]], max_heading_length: int = 100) -> Dict[str, Any]:
    """
    Prepare input data for LLM analysis.

    Args:
        candidates: List of candidate heading lines
        max_heading_length: Maximum length of heading content to include

    Returns:
        Dictionary formatted for LLM input
    """
    # Create input structure with line numbers and content
    input_lines = []
    for c in candidates:
        content = c['content'].strip()

        # Truncate long headings to prevent API errors
        if len(content) > max_heading_length:
            content = content[:max_heading_length] + "..."

        input_lines.append(f"{c['line_num']}: {content}")

    # Construct the prompt for LLM analysis
    llm_input = {
        "task_description": """Markdown标题修正任务，请按以下规则处理：

1. 格式识别：
   - 识别真正的标题行（应该转换为markdown标题）
   - 识别伪标题（看起来像标题但实际是正文）
   - 识别无效标题（空标题或无意义内容）

2. 层级规则：
   - 标题层级应该连续，不跳级（h1->h2->h3，不能h1->h3）
   - 文档第一个标题必须是h1
   - 同级标题应该有相同的缩进层级

3. 处理规则：
   - 保留数字编号（如"1.1 标题"转换为"## 1.1 标题"）
   - 删除空标题或无意义标题
   - 对于没有明确层级的标题，根据上下文推断合适层级

4. 输出格式：
   请直接返回JSON格式，不要包含其他文本：
   {
     "corrections": [
       {
         "line_number": 行号,
         "original_line": "原始内容",
         "corrected_line": "修正后的markdown标题",
         "reason": "修正原因"
       }
     ],
     "structure": [
       {
         "level": 标题层级(1-6),
         "content": "标题内容",
         "line_num": 行号
       }
     ]
   }""",
        "input_lines": "\n".join(input_lines)
    }

    return llm_input


def analyze_with_llm(llm_input: Dict[str, Any], config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Send data to OpenAI for analysis and receive correction suggestions.

    Args:
        llm_input: Formatted input for LLM
        config: Configuration options for OpenAI

    Returns:
        LLM response with correction information
    """
    if config is None:
        config = {
            "provider": "rule_based",
            "api_key": None,
            "base_url": None,
            "model": None
        }

    try:
        provider = config.get("provider", "rule_based")

        if provider == "openai":
            return _call_openai_api(llm_input, config)
        else:
            # Use rule-based approach as fallback
            return _rule_based_correction(llm_input)

    except Exception as e:
        print(f"Error during analysis: {e}")
        print("Falling back to rule-based correction...")
        return _rule_based_correction(llm_input)


def _call_openai_api(llm_input: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
    """Call OpenAI-compatible API for heading analysis."""
    try:
        api_key = config.get("api_key")
        base_url = config.get("base_url", "https://api.openai.com/v1")
        model = config.get("model", "gpt-4")

        if not api_key:
            raise ValueError("API key is required for OpenAI provider")

        # Ensure proper endpoint URL
        if not base_url.endswith("/"):
            base_url += "/"
        endpoint = f"{base_url}chat/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # Construct the full prompt for better compatibility
        full_prompt = f"{llm_input['task_description']}\n\n待处理的疑似标题行：\n{llm_input['input_lines']}"

        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "temperature": 0.2,
            "max_tokens": 4000
        }

        # Try to use JSON format if supported, otherwise fallback to regular response
        try:
            payload["response_format"] = {"type": "json_object"}
        except:
            pass  # Some APIs might not support this parameter

        print(f"Calling API at {endpoint}...")
        response = requests.post(endpoint, headers=headers, json=payload, timeout=300)
        response.raise_for_status()

        response_data = response.json()

        # Extract content from response
        content = response_data["choices"][0]["message"]["content"]

        # Check if content is None or empty
        if content is None:
            print("API returned None content")
            raise ValueError("API returned empty response")

        # Try to parse as JSON, fallback to text parsing if needed
        try:
            if isinstance(content, str):
                # Clean up the response to extract JSON
                content = content.strip()
                if not content:
                    raise ValueError("API returned empty string")

                # Find JSON content between ```json and ``` or just parse directly
                if "```json" in content:
                    start = content.find("```json") + 7
                    end = content.find("```", start)
                    if end != -1:
                        content = content[start:end].strip()
                elif content.startswith("```") and content.endswith("```"):
                    content = content[3:-3].strip()

                llm_response = json.loads(content)
            else:
                llm_response = content
        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON response: {e}")
            print(f"Raw response: {content[:500] if content else 'None'}...")
            # Fallback to rule-based if JSON parsing fails
            raise ValueError("Failed to parse LLM response as JSON")

        return extract_minimal_content(llm_response)

    except Exception as e:
        print(f"API error: {e}")
        raise





def _rule_based_correction(llm_input: Dict[str, Any]) -> Dict[str, Any]:
    """
    Rule-based heading correction with strict filtering.
    Only processes genuine structural headings.
    """
    corrections = []
    structure = []

    # Parse input lines
    candidates = []
    for line in llm_input['input_lines'].splitlines():
        if not line.strip():
            continue
        parts = line.split(":", 1)
        if len(parts) == 2:
            try:
                line_num = int(parts[0])
                content = parts[1].strip()
                candidates.append({"line_num": line_num, "content": content})
            except ValueError:
                continue

    for i, candidate in enumerate(candidates):
        line_num = candidate["line_num"]
        content = candidate["content"]

        # Skip obvious non-headings
        if _is_operation_step(content):
            continue

        # Determine heading level and format
        corrected_line = None
        level = 1  # Default level

        # 1. Document title (first genuine heading)
        if i == 0 or (len(structure) == 0 and re.search(r'(手册|文档|指南|说明书)', content)):
            level = 1
            clean_content = content.lstrip('#').strip()
            corrected_line = '# ' + clean_content

        # 2. Main numbered sections (1. 2. 3.)
        elif re.match(r'^[0-9]+\.?\s+[^\d]', content):
            # Check if this is the first main section, if so make it H1, otherwise H2
            if len(structure) == 0 or not any(s["level"] == 1 for s in structure):
                level = 1
            else:
                level = 2
            clean_content = content.lstrip('#').strip()
            corrected_line = '#' * level + ' ' + clean_content

        # 3. Sub-sections (1.1, 1.2, etc.)
        elif re.match(r'^[0-9]+\.[0-9]+\.?\s+', content):
            level = 3
            clean_content = content.lstrip('#').strip()
            corrected_line = '### ' + clean_content

        # 4. Deep sub-sections (1.1.1, etc.)
        elif re.match(r'^[0-9]+\.[0-9]+\.[0-9]+\.?\s+', content):
            level = 4
            clean_content = content.lstrip('#').strip()
            corrected_line = '#### ' + clean_content

        # 5. Chinese numbered (1、2、)
        elif re.match(r'^[0-9]+[、，]\s*', content):
            level = 3  # Usually sub-sections
            clean_content = content.lstrip('#').strip()
            corrected_line = '### ' + clean_content

        # 6. Chinese chapters
        elif re.match(r'^第[零一二三四五六七八九十]+[章节部分]', content):
            level = 2
            clean_content = content.lstrip('#').strip()
            corrected_line = '## ' + clean_content

        # 7. Functional modules - REMOVED
        # These should be treated as regular content, not headings

        else:
            # Skip - not a genuine heading
            continue

        corrections.append({
            "line_number": line_num,
            "original_line": content,
            "corrected_line": corrected_line,
            "reason": f"Converted to level {level} heading"
        })

        structure.append({
            "level": level,
            "content": content,
            "line_num": line_num
        })

    return {
        "corrections": corrections,
        "structure": structure
    }


def _is_operation_step(content: str) -> bool:
    """
    Determine if a line is an operation step rather than a heading.
    """
    # Operation verbs
    if re.search(r'(点击|扫描|输入|选择|填写|完成|提交|确认|返回|弹出|跳转)', content):
        return True

    # UI elements
    if re.search(r'(按钮|页面|功能|操作|步骤|任务|订单|商品|条码|弹窗|列表|字段)', content):
        return True

    # Descriptive phrases
    if re.search(r'(显示在|出现在|位于|包含|具有|用于|进行|执行)', content):
        return True

    # Long descriptive sentences
    if len(content) > 100:
        return True

    # Ends with punctuation (likely description)
    if re.search(r'[。！？：；]$', content):
        return True

    return False


def update_markdown_content(
    original_lines: List[str],
    corrections: List[Dict[str, Any]],
    line_ending: str
) -> str:
    """
    Apply corrections to the original markdown content.

    Args:
        original_lines: Original file content as lines
        corrections: Corrections from analysis
        line_ending: Line ending style to use

    Returns:
        Updated file content as string
    """
    updated_lines = original_lines.copy()

    # Sort corrections by line number in descending order to avoid index shifting
    sorted_corrections = sorted(
        corrections,
        key=lambda x: x["line_number"],
        reverse=True
    )

    lines_to_delete = []

    # Apply corrections
    for correction in sorted_corrections:
        line_num = correction["line_number"]
        position = line_num - 1  # Convert to 0-indexed

        if position < 0 or position >= len(updated_lines):
            print(f"Warning: Line {line_num} is out of range, skipping...")
            continue

        corrected_line = correction.get("corrected_line")

        if corrected_line is None or corrected_line.strip() == "":
            # Mark line for deletion
            lines_to_delete.append(position)
        else:
            # Clean the corrected line
            cleaned_line = corrected_line.strip()
            # Remove any line number prefixes that might have been added
            cleaned_line = re.sub(r'^\d+:\s*', '', cleaned_line)
            updated_lines[position] = cleaned_line

    # Remove lines marked for deletion
    for position in sorted(lines_to_delete, reverse=True):
        if 0 <= position < len(updated_lines):
            del updated_lines[position]

    # Join the lines with the original line ending
    updated_content = line_ending.join(updated_lines)

    # Ensure the file ends with a newline if it's not empty
    if updated_lines and not updated_content.endswith(line_ending):
        updated_content += line_ending

    return updated_content


def extract_minimal_content(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract only the most essential information from the LLM response.
    
    Args:
        response: The full LLM response
        
    Returns:
        Minimized response with only essential data
    """
    minimal_response = {"corrections": [], "structure": []}
    
    # Extract minimal correction information
    if "corrections" in response:
        for correction in response["corrections"]:
            minimal_correction = {
                "line_number": correction["line_number"],
                "corrected_line": correction.get("corrected_line")
            }
            minimal_response["corrections"].append(minimal_correction)
    
    # Extract minimal structure information
    if "structure" in response:
        for item in response["structure"]:
            minimal_item = {
                "level": item.get("level"),
                "line_num": item.get("line_num")
            }
            minimal_response["structure"].append(minimal_item)
    
    return minimal_response


def main() -> None:
    """Main function to process markdown files."""
    parser = argparse.ArgumentParser(
        description="Markdown Heading Fixer - A tool to intelligently correct heading formats and hierarchy",
        epilog=f"Version {__version__} - For more information, see README_markdown_fixer.md"
    )
    parser.add_argument(
        "--version",
        action="version",
        version=f"Markdown Heading Fixer {__version__}"
    )
    parser.add_argument(
        "file_path",
        help="Path to the markdown file to process"
    )
    parser.add_argument(
        "-o", "--output",
        help="Output file path. If not provided, the input file will be modified in-place"
    )
    parser.add_argument(
        "-d", "--dry-run",
        action="store_true",
        help="Show corrections without modifying the file"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Show detailed output about the corrections"
    )
    parser.add_argument(
        "--max-heading-length",
        type=int,
        default=100,
        help="Maximum length of heading content to include in API request (default: 100)"
    )

    # LLM API configuration
    llm_group = parser.add_argument_group('LLM Configuration')
    llm_group.add_argument(
        "--llm-provider",
        choices=["openai", "rule_based"],
        default="rule_based",
        help="Provider to use for analysis (default: rule_based)"
    )
    llm_group.add_argument(
        "--api-key",
        help="API key for OpenAI (can also be set via OPENAI_API_KEY environment variable)"
    )
    llm_group.add_argument(
        "--base-url",
        help="Base URL for OpenAI API (for custom endpoints, default: https://api.openai.com/v1)"
    )
    llm_group.add_argument(
        "--model",
        help="OpenAI model to use for analysis (e.g., gpt-4, gpt-3.5-turbo)"
    )

    args = parser.parse_args()

    # Check if the file exists
    if not os.path.isfile(args.file_path):
        print(f"Error: File {args.file_path} does not exist")
        sys.exit(1)

    # Read the markdown file
    lines, line_ending, encoding = read_markdown_file(args.file_path)

    # Clean existing heading formats first
    cleaned_lines = clean_existing_headings(lines)

    # Extract heading candidates from cleaned content
    candidates = extract_heading_candidates(cleaned_lines)

    if not candidates:
        print("No heading candidates found in the file.")
        sys.exit(0)

    if args.verbose:
        print(f"Found {len(candidates)} heading candidates:")
        for candidate in candidates:
            print(f"  Line {candidate['line_num']}: {candidate['content'][:80]}...")

    # Prepare data for analysis
    llm_input = prepare_llm_input(candidates, max_heading_length=args.max_heading_length)

    # Configure analysis method
    llm_config = {
        "provider": args.llm_provider,
        "api_key": args.api_key or os.environ.get("OPENAI_API_KEY"),
        "base_url": args.base_url,
        "model": args.model
    }

    # Validate API configuration if using OpenAI
    if args.llm_provider == "openai" and not llm_config["api_key"]:
        print(f"Warning: No API key provided for OpenAI. Falling back to rule-based correction.")
        llm_config["provider"] = "rule_based"

    # Perform analysis
    try:
        print(f"Analyzing headings using {llm_config['provider']} method...")
        analysis_response = analyze_with_llm(llm_input, config=llm_config)
    except Exception as e:
        print(f"Analysis failed: {e}")
        print("Falling back to rule-based correction...")
        llm_config["provider"] = "rule_based"
        analysis_response = analyze_with_llm(llm_input, config=llm_config)

    # Process corrections
    if "corrections" not in analysis_response or not analysis_response["corrections"]:
        print("No corrections needed.")
        sys.exit(0)

    if args.verbose or args.dry_run:
        print(f"\n{len(analysis_response['corrections'])} corrections to be applied:")
        for correction in analysis_response["corrections"]:
            line_num = correction["line_number"]
            original = correction.get("original_line", "")
            corrected = correction.get("corrected_line", "DELETE")
            reason = correction.get("reason", "")
            print(f"  Line {line_num}: {original[:50]}... -> {corrected[:50]}...")
            if reason and args.verbose:
                print(f"    Reason: {reason}")

    if args.verbose:
        print("\nHeading structure:")
        for heading in analysis_response.get("structure", []):
            level = heading.get("level")
            line_num = heading.get("line_num")
            content = heading.get("content", "")
            print(f"  H{level} (Line {line_num}): {content[:60]}...")

    if not args.dry_run:
        # Update content based on corrections (use cleaned lines as base)
        updated_content = update_markdown_content(
            cleaned_lines,
            analysis_response["corrections"],
            line_ending
        )

        # Determine output path
        output_path = args.output if args.output else args.file_path

        # Write to output file
        try:
            with open(output_path, 'w', encoding=encoding, newline='') as f:
                f.write(updated_content)
            print(f"Successfully updated markdown headings in {output_path}")
            print(f"Applied {len(analysis_response['corrections'])} corrections.")
        except Exception as e:
            print(f"Error writing to file {output_path}: {e}")
            sys.exit(1)
    else:
        print("Dry run completed. No changes were made to the file.")


if __name__ == "__main__":
    main()
    