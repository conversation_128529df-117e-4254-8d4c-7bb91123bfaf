#!/usr/bin/env python3
"""
配置示例文件 - Markdown标题修正工具
使用您提供的大模型API配置
"""

# 您的大模型API配置
LLM_CONFIG = {
    "provider": "openai",  # 使用OpenAI兼容的API
    "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
    "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Dbapi_key",
    "model": "gpt-4"  # 您可以根据实际可用的模型调整
}

# 使用示例
def example_usage():
    """使用示例"""
    import subprocess
    import sys
    
    # 示例1: 使用大模型分析（推荐）
    cmd_llm = [
        sys.executable, "markdown_heading_fixer.py",
        "your_markdown_file.md",  # 替换为您的MD文件路径
        "--llm-provider", "openai",
        "--base-url", LLM_CONFIG["base_url"],
        "--api-key", LLM_CONFIG["api_key"],
        "--model", LLM_CONFIG["model"],
        "--verbose"  # 显示详细信息
    ]
    
    # 示例2: 先预览修改（dry-run）
    cmd_preview = [
        sys.executable, "markdown_heading_fixer.py",
        "your_markdown_file.md",
        "--llm-provider", "openai",
        "--base-url", LLM_CONFIG["base_url"],
        "--api-key", LLM_CONFIG["api_key"],
        "--model", LLM_CONFIG["model"],
        "--dry-run",  # 只预览，不实际修改
        "--verbose"
    ]
    
    # 示例3: 使用规则引擎（不需要API）
    cmd_rule = [
        sys.executable, "markdown_heading_fixer.py",
        "your_markdown_file.md",
        "--llm-provider", "rule_based",
        "--verbose"
    ]
    
    print("使用大模型分析的命令:")
    print(" ".join(cmd_llm))
    print("\n预览修改的命令:")
    print(" ".join(cmd_preview))
    print("\n使用规则引擎的命令:")
    print(" ".join(cmd_rule))

if __name__ == "__main__":
    example_usage()
