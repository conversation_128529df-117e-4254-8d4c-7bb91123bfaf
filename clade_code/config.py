#!/usr/bin/env python3
"""
配置文件 - 请在这里更新您的API配置
"""

# 大模型API配置
# 请根据您的实际API信息更新以下配置
LLM_CONFIG = {
    "provider": "openai",  # 使用OpenAI兼容的API
    "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
    "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
    "model": "Qwen3-235B-A22B"  # 使用您指定的模型
}

# 如果您不确定API配置，可以先使用规则引擎
FALLBACK_TO_RULES = True  # 如果API失败，是否自动回退到规则引擎

# 其他配置选项
DEFAULT_OPTIONS = {
    "max_heading_length": 100,  # 发送给API的标题最大长度
    "verbose": True,  # 是否显示详细信息
    "dry_run": False,  # 是否只预览不修改
}

def get_config():
    """获取配置"""
    return {
        "llm": LLM_CONFIG,
        "fallback": FALLBACK_TO_RULES,
        "options": DEFAULT_OPTIONS
    }

def update_api_key(new_api_key):
    """更新API密钥"""
    global LLM_CONFIG
    LLM_CONFIG["api_key"] = new_api_key
    print(f"API密钥已更新")

def update_base_url(new_base_url):
    """更新API基础URL"""
    global LLM_CONFIG
    LLM_CONFIG["base_url"] = new_base_url
    print(f"API基础URL已更新为: {new_base_url}")

def update_model(new_model):
    """更新模型名称"""
    global LLM_CONFIG
    LLM_CONFIG["model"] = new_model
    print(f"模型已更新为: {new_model}")

if __name__ == "__main__":
    print("当前配置:")
    config = get_config()
    print(f"API提供商: {config['llm']['provider']}")
    print(f"API基础URL: {config['llm']['base_url']}")
    print(f"API密钥: {config['llm']['api_key'][:20]}..." if len(config['llm']['api_key']) > 20 else config['llm']['api_key'])
    print(f"模型: {config['llm']['model']}")
    print(f"自动回退到规则引擎: {config['fallback']}")
